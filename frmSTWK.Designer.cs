﻿
namespace HIH.CRM.Import
{
    partial class frmSTWK
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmSTWK));
            this.barMenu = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.btnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.btnImport = new DevExpress.XtraBars.BarButtonItem();
            this.btnExit = new DevExpress.XtraBars.BarButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.btnSave = new DevExpress.XtraBars.BarButtonItem();
            this.btnDel = new DevExpress.XtraBars.BarButtonItem();
            this.panel1 = new System.Windows.Forms.Panel();
            this.btnSearch = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.cbbOrderStatus = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtRow = new DevExpress.XtraEditors.TextEdit();
            this.lblOrder = new DevExpress.XtraEditors.LabelControl();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.gd = new DevExpress.XtraGrid.GridControl();
            this.gdv = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.订单状态 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.订单剩余数量 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.订单号 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.行号 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.供应商代码 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.供应商名称 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.料号 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.料号名称 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.订单数量 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.剩余数量 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.单位 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.状态 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.交付日期 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.延后交期 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.运输时间 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.bs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barMenu)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbbOrderStatus.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRow.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gd)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdv)).BeginInit();
            this.SuspendLayout();
            // 
            // barMenu
            // 
            this.barMenu.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barMenu.DockControls.Add(this.barDockControlTop);
            this.barMenu.DockControls.Add(this.barDockControlBottom);
            this.barMenu.DockControls.Add(this.barDockControlLeft);
            this.barMenu.DockControls.Add(this.barDockControlRight);
            this.barMenu.Form = this;
            this.barMenu.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.btnRefresh,
            this.btnExit,
            this.btnSave,
            this.btnDel,
            this.btnImport});
            this.barMenu.MaxItemId = 9;
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.btnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnImport),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnExit, true)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.Text = "Tools";
            // 
            // btnRefresh
            // 
            this.btnRefresh.Caption = "刷新";
            this.btnRefresh.Id = 0;
            this.btnRefresh.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnRefresh.ImageOptions.SvgImage")));
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnRefresh_ItemClick);
            // 
            // btnImport
            // 
            this.btnImport.Caption = "导入";
            this.btnImport.Id = 8;
            this.btnImport.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnImport.ImageOptions.Image")));
            this.btnImport.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnImport.ImageOptions.LargeImage")));
            this.btnImport.Name = "btnImport";
            this.btnImport.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnImport.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnImport_ItemClick);
            // 
            // btnExit
            // 
            this.btnExit.Caption = "返回";
            this.btnExit.Id = 1;
            this.btnExit.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnExit.ImageOptions.SvgImage")));
            this.btnExit.Name = "btnExit";
            this.btnExit.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barMenu;
            this.barDockControlTop.Size = new System.Drawing.Size(1316, 24);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 594);
            this.barDockControlBottom.Manager = this.barMenu;
            this.barDockControlBottom.Size = new System.Drawing.Size(1316, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 24);
            this.barDockControlLeft.Manager = this.barMenu;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 570);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1316, 24);
            this.barDockControlRight.Manager = this.barMenu;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 570);
            // 
            // btnSave
            // 
            this.btnSave.Caption = "保存";
            this.btnSave.Id = 3;
            this.btnSave.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnSave.ImageOptions.SvgImage")));
            this.btnSave.Name = "btnSave";
            this.btnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // btnDel
            // 
            this.btnDel.Id = 7;
            this.btnDel.Name = "btnDel";
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.btnSearch);
            this.panel1.Controls.Add(this.labelControl2);
            this.panel1.Controls.Add(this.cbbOrderStatus);
            this.panel1.Controls.Add(this.labelControl1);
            this.panel1.Controls.Add(this.txtRow);
            this.panel1.Controls.Add(this.lblOrder);
            this.panel1.Controls.Add(this.textEdit1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 24);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1316, 81);
            this.panel1.TabIndex = 4;
            // 
            // btnSearch
            // 
            this.btnSearch.Appearance.BackColor = DevExpress.LookAndFeel.DXSkinColors.FillColors.Success;
            this.btnSearch.Appearance.Options.UseBackColor = true;
            this.btnSearch.Location = new System.Drawing.Point(584, 18);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(81, 23);
            this.btnSearch.TabIndex = 499;
            this.btnSearch.Text = "查询";
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(369, 22);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(48, 14);
            this.labelControl2.TabIndex = 5;
            this.labelControl2.Text = "订单状态";
            // 
            // cbbOrderStatus
            // 
            this.cbbOrderStatus.Location = new System.Drawing.Point(423, 19);
            this.cbbOrderStatus.MenuManager = this.barMenu;
            this.cbbOrderStatus.Name = "cbbOrderStatus";
            this.cbbOrderStatus.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbbOrderStatus.Properties.Items.AddRange(new object[] {
            "未完成",
            "核销完成"});
            this.cbbOrderStatus.Size = new System.Drawing.Size(100, 20);
            this.cbbOrderStatus.TabIndex = 4;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(217, 22);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(24, 14);
            this.labelControl1.TabIndex = 3;
            this.labelControl1.Text = "行号";
            // 
            // txtRow
            // 
            this.txtRow.Location = new System.Drawing.Point(247, 19);
            this.txtRow.MenuManager = this.barMenu;
            this.txtRow.Name = "txtRow";
            this.txtRow.Size = new System.Drawing.Size(100, 20);
            this.txtRow.TabIndex = 2;
            // 
            // lblOrder
            // 
            this.lblOrder.Location = new System.Drawing.Point(55, 22);
            this.lblOrder.Name = "lblOrder";
            this.lblOrder.Size = new System.Drawing.Size(36, 14);
            this.lblOrder.TabIndex = 1;
            this.lblOrder.Text = "订单号";
            // 
            // textEdit1
            // 
            this.textEdit1.Location = new System.Drawing.Point(92, 19);
            this.textEdit1.MenuManager = this.barMenu;
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Size = new System.Drawing.Size(100, 20);
            this.textEdit1.TabIndex = 0;
            // 
            // gd
            // 
            this.gd.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gd.Location = new System.Drawing.Point(0, 105);
            this.gd.MainView = this.gdv;
            this.gd.MenuManager = this.barMenu;
            this.gd.Name = "gd";
            this.gd.Size = new System.Drawing.Size(1316, 489);
            this.gd.TabIndex = 5;
            this.gd.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gdv});
            // 
            // gdv
            // 
            this.gdv.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.订单状态,
            this.订单剩余数量,
            this.订单号,
            this.行号,
            this.供应商代码,
            this.供应商名称,
            this.料号,
            this.料号名称,
            this.订单数量,
            this.剩余数量,
            this.单位,
            this.状态,
            this.交付日期,
            this.延后交期,
            this.运输时间});
            this.gdv.GridControl = this.gd;
            this.gdv.Name = "gdv";
            this.gdv.OptionsSelection.MultiSelect = true;
            this.gdv.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect;
            this.gdv.OptionsView.ColumnAutoWidth = false;
            this.gdv.OptionsView.ShowGroupPanel = false;
            this.gdv.MouseDown += new System.Windows.Forms.MouseEventHandler(this.gdv_MouseDown);
            // 
            // 订单状态
            // 
            this.订单状态.Caption = "订单状态";
            this.订单状态.FieldName = "订单状态";
            this.订单状态.Name = "订单状态";
            this.订单状态.Visible = true;
            this.订单状态.VisibleIndex = 1;
            this.订单状态.Width = 88;
            // 
            // 订单剩余数量
            // 
            this.订单剩余数量.Caption = "订单剩余数量";
            this.订单剩余数量.FieldName = "订单剩余数量";
            this.订单剩余数量.Name = "订单剩余数量";
            this.订单剩余数量.Visible = true;
            this.订单剩余数量.VisibleIndex = 15;
            // 
            // 订单号
            // 
            this.订单号.Caption = "订单号";
            this.订单号.FieldName = "订单号";
            this.订单号.Name = "订单号";
            this.订单号.Visible = true;
            this.订单号.VisibleIndex = 2;
            // 
            // 行号
            // 
            this.行号.Caption = "行号";
            this.行号.FieldName = "行号";
            this.行号.Name = "行号";
            this.行号.Visible = true;
            this.行号.VisibleIndex = 3;
            // 
            // 供应商代码
            // 
            this.供应商代码.Caption = "供应商代码";
            this.供应商代码.FieldName = "供应商代码";
            this.供应商代码.Name = "供应商代码";
            this.供应商代码.Visible = true;
            this.供应商代码.VisibleIndex = 4;
            // 
            // 供应商名称
            // 
            this.供应商名称.Caption = "供应商名称";
            this.供应商名称.FieldName = "供应商名称";
            this.供应商名称.Name = "供应商名称";
            this.供应商名称.Visible = true;
            this.供应商名称.VisibleIndex = 5;
            // 
            // 料号
            // 
            this.料号.Caption = "料号";
            this.料号.FieldName = "料号";
            this.料号.Name = "料号";
            this.料号.Visible = true;
            this.料号.VisibleIndex = 6;
            // 
            // 料号名称
            // 
            this.料号名称.Caption = "料号名称";
            this.料号名称.FieldName = "料号名称";
            this.料号名称.Name = "料号名称";
            this.料号名称.Visible = true;
            this.料号名称.VisibleIndex = 7;
            // 
            // 订单数量
            // 
            this.订单数量.Caption = "订单数量";
            this.订单数量.FieldName = "订单数量";
            this.订单数量.Name = "订单数量";
            this.订单数量.Visible = true;
            this.订单数量.VisibleIndex = 8;
            // 
            // 剩余数量
            // 
            this.剩余数量.Caption = "剩余数量";
            this.剩余数量.FieldName = "剩余数量";
            this.剩余数量.Name = "剩余数量";
            this.剩余数量.Visible = true;
            this.剩余数量.VisibleIndex = 9;
            // 
            // 单位
            // 
            this.单位.Caption = "单位";
            this.单位.FieldName = "单位";
            this.单位.Name = "单位";
            this.单位.Visible = true;
            this.单位.VisibleIndex = 10;
            // 
            // 状态
            // 
            this.状态.Caption = "状态";
            this.状态.FieldName = "状态";
            this.状态.Name = "状态";
            this.状态.Visible = true;
            this.状态.VisibleIndex = 11;
            // 
            // 交付日期
            // 
            this.交付日期.Caption = "交付日期";
            this.交付日期.FieldName = "交付日期";
            this.交付日期.Name = "交付日期";
            this.交付日期.Visible = true;
            this.交付日期.VisibleIndex = 12;
            // 
            // 延后交期
            // 
            this.延后交期.Caption = "延后交期";
            this.延后交期.FieldName = "延后交期";
            this.延后交期.Name = "延后交期";
            this.延后交期.Visible = true;
            this.延后交期.VisibleIndex = 13;
            this.延后交期.Width = 98;
            // 
            // 运输时间
            // 
            this.运输时间.Caption = "运输时间";
            this.运输时间.FieldName = "运输时间";
            this.运输时间.Name = "运输时间";
            this.运输时间.Visible = true;
            this.运输时间.VisibleIndex = 14;
            this.运输时间.Width = 98;
            // 
            // frmSTWK
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1316, 594);
            this.Controls.Add(this.gd);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frmSTWK";
            this.Text = "frmSTWK";
            ((System.ComponentModel.ISupportInitialize)(this.bs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barMenu)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbbOrderStatus.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRow.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gd)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdv)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        public DevExpress.XtraBars.BarManager barMenu;
        public DevExpress.XtraBars.Bar bar1;
        public DevExpress.XtraBars.BarButtonItem btnRefresh;
        private DevExpress.XtraBars.BarButtonItem btnSave;
        public DevExpress.XtraBars.BarButtonItem btnExit;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem btnDel;
        private DevExpress.XtraGrid.GridControl gd;
        private DevExpress.XtraGrid.Views.Grid.GridView gdv;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraGrid.Columns.GridColumn 订单号;
        private DevExpress.XtraGrid.Columns.GridColumn 行号;
        private DevExpress.XtraGrid.Columns.GridColumn 供应商代码;
        private DevExpress.XtraGrid.Columns.GridColumn 供应商名称;
        private DevExpress.XtraGrid.Columns.GridColumn 料号;
        private DevExpress.XtraGrid.Columns.GridColumn 料号名称;
        private DevExpress.XtraGrid.Columns.GridColumn 订单数量;
        private DevExpress.XtraGrid.Columns.GridColumn 剩余数量;
        private DevExpress.XtraGrid.Columns.GridColumn 单位;
        private DevExpress.XtraGrid.Columns.GridColumn 状态;
        private DevExpress.XtraGrid.Columns.GridColumn 交付日期;
        private DevExpress.XtraGrid.Columns.GridColumn 延后交期;
        private DevExpress.XtraGrid.Columns.GridColumn 运输时间;
        private DevExpress.XtraGrid.Columns.GridColumn 订单状态;
        private DevExpress.XtraGrid.Columns.GridColumn 订单剩余数量;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit txtRow;
        private DevExpress.XtraEditors.LabelControl lblOrder;
        private DevExpress.XtraEditors.TextEdit textEdit1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.ComboBoxEdit cbbOrderStatus;
        private DevExpress.XtraEditors.SimpleButton btnSearch;
        private DevExpress.XtraBars.BarButtonItem btnImport;
    }
}